import { FastifyInstance } from 'fastify'
import fp from 'fastify-plugin'
import Sentry from '../infras/sentry'
import { successResponse } from '../common/response/response'

async function stripeWebhookPlugin(fastify: FastifyInstance) {
  fastify.removeContentTypeParser('application/json')
  fastify.addContentTypeParser(
    'application/json',
    { parseAs: 'string' },
    (req, body: string, done) => {
      // 只处理webhook路由
      if (req.url === '/api/subscription/webhook') {
        req.rawBody = body
        try {
          const json = JSON.parse(body)
          done(null, json)
        } catch (err) {
          done(err as Error)
        }
      } else {
        // 其他路由使用默认解析
        try {
          const json = JSON.parse(body)
          done(null, json)
        } catch (err) {
          done(err as Error)
        }
      }
    },
  )

  fastify.addHook('onError', (request, reply, error, done) => {
    if (request.url.includes('/api/subscription/webhook')) {
      fastify.log.error('Stripe webhook处理出错:', error)
      Sentry.captureException(error)
      // 对于Stripe webhook请求，即使出错也返回200，以防止Stripe重试
      if (!reply.sent) {
        reply.code(200).send(successResponse({ received: true }))
      }
    }
    done()
  })
}

export default fp(stripeWebhookPlugin)
